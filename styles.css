/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Cores da paleta */
:root {
    --verde-claro: #01d800;
    --verde-escuro: #217345;
    --vermelho: #fd0e35;
    --amarelo: #ffe206;
    --azul-claro: #0593ff;
    --azul-escuro: #005aec;
    --branco: #ffffff;
    --cinza-claro: #f8f9fa;
    --cinza-escuro: #333333;
}

/* Header */
.header {
    background: var(--branco);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo img {
    height: 60px;
    width: auto;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: var(--cinza-escuro);
    font-weight: 500;
    transition: color 0.3s;
}

.nav a:hover,
.nav a.active {
    color: var(--azul-escuro);
}

.btn-troca {
    background: var(--verde-claro);
    color: var(--branco);
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: background 0.3s;
}

.btn-troca:hover {
    background: var(--verde-escuro);
}

.btn-troca.active {
    background: var(--verde-escuro);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--azul-escuro), var(--azul-claro));
    color: var(--branco);
    padding: 120px 0 80px;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.btn-primary {
    background: var(--verde-claro);
    color: var(--branco);
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: bold;
    transition: background 0.3s;
    display: inline-block;
}

.btn-primary:hover {
    background: var(--verde-escuro);
}

/* Sobre Section */
.sobre {
    padding: 80px 0;
    background: var(--cinza-claro);
}

.sobre h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--azul-escuro);
}

.sobre-content p {
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.sobre-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.sobre-item {
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.sobre-item h3 {
    color: var(--azul-escuro);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

/* Serviços Section */
.servicos {
    padding: 80px 0;
    background: var(--branco);
}

.servicos h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: var(--azul-escuro);
}

.servicos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.servico-item {
    background: var(--cinza-claro);
    padding: 2rem;
    border-radius: 10px;
    border-left: 5px solid var(--verde-claro);
}

.servico-item h3 {
    color: var(--azul-escuro);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.servico-item ul {
    list-style: none;
}

.servico-item li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ddd;
}

.servico-item li:before {
    content: "✓";
    color: var(--verde-claro);
    font-weight: bold;
    margin-right: 0.5rem;
}

/* Formulário de Contato */
.contato {
    padding: 80px 0;
    background: var(--cinza-claro);
}

.form-title {
    text-align: center;
    margin-bottom: 3rem;
}

.form-title h2 {
    background: var(--amarelo);
    color: var(--cinza-escuro);
    padding: 1rem;
    border-radius: 5px;
    font-weight: bold;
    display: inline-block;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-group label {
    min-width: 150px;
    font-weight: bold;
}

.form-group label .required,
.required {
    color: var(--vermelho);
}

.form-group input,
.form-group textarea {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.required-label {
    font-weight: bold;
    margin-bottom: 1rem;
    display: block;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-left: 1rem;
}

.radio-group label {
    min-width: auto;
    font-weight: normal;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-submit {
    background: var(--verde-claro);
    color: var(--branco);
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
    margin-top: 1rem;
}

.btn-submit:hover {
    background: var(--verde-escuro);
}

.observacoes {
    max-width: 600px;
    margin: 2rem auto 0;
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
}

.observacoes h3 {
    background: var(--amarelo);
    color: var(--cinza-escuro);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 1rem;
}

.observacoes ol {
    padding-left: 1rem;
}

.observacoes li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* Botão WhatsApp Flutuante */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.whatsapp-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #25D366;
    color: var(--branco);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.whatsapp-button:hover {
    background: #128C7E;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

.whatsapp-button svg {
    width: 24px;
    height: 24px;
}

.whatsapp-text {
    font-size: 0.9rem;
}

@keyframes pulse {
    0% {
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    }
    50% {
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.8);
    }
    100% {
        box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    }
}

/* Footer */
.footer {
    background: var(--azul-escuro);
    color: var(--branco);
    text-align: center;
    padding: 2rem 0;
}

/* Responsividade */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav ul {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .form-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .form-group label {
        min-width: auto;
    }

    /* WhatsApp button mobile */
    .whatsapp-float {
        bottom: 15px;
        right: 15px;
    }

    .whatsapp-button {
        padding: 0.75rem 1rem;
        border-radius: 50px;
    }

    .whatsapp-text {
        display: none;
    }

    .whatsapp-button svg {
        width: 20px;
        height: 20px;
    }
}
